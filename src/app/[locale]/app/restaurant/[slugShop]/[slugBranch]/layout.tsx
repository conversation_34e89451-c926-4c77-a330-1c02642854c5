'use client';

import { NavigationProvider } from '@/lib/context/NavigationContext';
import { NavigationType } from '@/lib/types/navigation';
import { addIconsToNavItems } from '@/components/navigation/NavigationIcons';
import ExpandableHeaderNavigation from '@/components/navigation/ExpandableHeaderNavigation';
import SheetSidebarNavigation from '@/components/navigation/SheetSidebarNavigation';
import DrawerNavigation from '@/components/navigation/DrawerNavigation';
import BottomNavigation from '@/components/navigation/BottomNavigation';
import { useNavigation } from '@/lib/context/NavigationContext';
import { cn } from '@/lib/utils';
import { useState, useEffect } from 'react';
import React from 'react';
import NotificationPopover from '@/components/notifications/NotificationPopover';
import ProfileMenu from '@/components/navigation/ProfileMenu';
import { getBranchWithShop } from '@/mock/shopData';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';
import LayoutSwitcher from '@/components/layout/LayoutSwitcher';

// Navigation wrapper component that uses the navigation context
function NavigationWrapper({
  children,
  slugShop,
  slugBranch
}: {
  children: React.ReactNode;
  slugShop: string;
  slugBranch: string;
}) {
  const { settings } = useNavigation();
  const [isCollapsed, setIsCollapsed] = useState(settings.isCollapsed || false);
  // Initialize with null to ensure proper type checking
  const [branchWithShop, setBranchWithShop] = useState(() => {
    const result = getBranchWithShop(slugShop, slugBranch);
    return result || null;
  });

  // Basic nav items without icons
  const basicNavItems = [
    { name: 'Dashboard', href: `/app/restaurant/${slugShop}/${slugBranch}/dashboard` },
    { name: 'Orders', href: `/app/restaurant/${slugShop}/${slugBranch}/orders` },
    { name: 'Menu', href: `/app/restaurant/${slugShop}/${slugBranch}/menu` },
    { name: 'Tables', href: `/app/restaurant/${slugShop}/${slugBranch}/tables` },
    { name: 'Reviews', href: `/app/restaurant/${slugShop}/${slugBranch}/reviews` },
    { name: 'Staff', href: `/app/restaurant/${slugShop}/${slugBranch}/staff` },
    { name: 'Reports', href: `/app/restaurant/${slugShop}/${slugBranch}/reports` },
    { name: 'Settings', href: `/app/restaurant/${slugShop}/${slugBranch}/settings` },
  ];

  // Add icons to nav items
  const navItems = addIconsToNavItems(basicNavItems);

  // Function to render the appropriate navigation based on settings
  const renderNavigation = () => {
    // Header for search and profile
    const renderHeader = () => (
      <div className="sticky top-0 z-10 w-full border-b border-[#e2dcd4] bg-[#fbfaf9] ">
        <div className="flex h-16 items-center justify-between px-4 ">
          <div className="flex items-center">
            {branchWithShop && branchWithShop.shop && branchWithShop.branch ? (
              <div className="text-[#181510] font-medium">
                {branchWithShop.shop.name} - {branchWithShop.branch.name}
              </div>
            ) : (
              <div className="text-[#181510] font-medium">
                Restaurant Dashboard
              </div>
            )}
          </div>
          <div className="flex items-center space-x-4">
            <button className="md:hidden text-[#181510] hover:text-[#8a745c] transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"
                ></path>
              </svg>
            </button>
            <LayoutSwitcher variant="compact" showLabel={false} />
            <NotificationPopover />
            <ProfileMenu />
          </div>
        </div>
      </div>
    );

    switch (settings.type) {
      case NavigationType.HEADER:
        return (
          <>
            {renderHeader()}
            <ExpandableHeaderNavigation
              navItems={navItems}
              showIcons={settings.showIcons}
              autoHide={settings.autoHide}
            />
          </>
        );
      case NavigationType.SIDEBAR_LEFT:
      case NavigationType.SIDEBAR_RIGHT:
        return (
          <>
            {renderHeader()}
            <SheetSidebarNavigation
              navItems={navItems}
              position={settings.type === NavigationType.SIDEBAR_LEFT ? 'left' : 'right'}
              isCollapsed={isCollapsed}
              setIsCollapsed={setIsCollapsed}
              showIcons={settings.showIcons}
            />
          </>
        );
      case NavigationType.DRAWER:
        return (
          <>
            {renderHeader()}
            <DrawerNavigation
              navItems={navItems}
              showIcons={settings.showIcons}
            />
          </>
        );
      case NavigationType.BOTTOM:
        return (
          <>
            {renderHeader()}
            <BottomNavigation
              navItems={navItems}
              showLabels={settings.showLabels}
              showIcons={settings.showIcons}
              autoHide={settings.autoHide}
            />
          </>
        );
      default:
        return (
          <>
            {renderHeader()}
            <ExpandableHeaderNavigation
              navItems={navItems}
              showIcons={settings.showIcons}
              autoHide={settings.autoHide}
            />
          </>
        );
    }
  };

  // Function to get the content class based on navigation type
  const getContentClass = () => {
    switch (settings.type) {
      case NavigationType.SIDEBAR_LEFT:
        return isCollapsed
          ? "ml-[80px]"
          : "ml-[240px]";
      case NavigationType.SIDEBAR_RIGHT:
        return isCollapsed
          ? "mr-[80px]"
          : "mr-[240px]";
      default:
        return "";
    }
  };

  return (
    <div className=" container flex size-full min-h-screen mx-auto flex-col bg-[#fbfaf9] font-be-vietnam overflow-x-hidden sticky top-0 ">
      {renderNavigation()}
      <div
        className={cn(
          "flex-1 transition-all duration-300",
          getContentClass()
        )}
      >
        <div className="flex flex-1 justify-center py-5 items-center pb-25">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1 mx-auto text-center ">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}

// Main layout component that provides the navigation context
export default async function BranchLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}) {
  // In Next.js 15, we need to await the params to avoid the error
  const { slugShop, slugBranch } = await params;

  return (
    <NavigationProvider>
      <NavigationWrapper slugShop={slugShop} slugBranch={slugBranch}>
        {children}
      </NavigationWrapper>
    </NavigationProvider>
  );
}
