'use client';

import { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { useGetTablesQuery } from '@/lib/redux/api/endpoints/restaurantApi';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import QRCode from 'qrcode';
import { ArrowLeft, Download, Printer } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';
import React from 'react';

// Mock data for tables (will be replaced with real data from API)
const mockTables = [
  {
    id: '1',
    number: 1,
    capacity: 4,
    status: 'available',
    location: 'dining',
  },
  {
    id: '2',
    number: 2,
    capacity: 2,
    status: 'available',
    location: 'dining',
  },
  {
    id: '3',
    number: 3,
    capacity: 6,
    status: 'available',
    location: 'dining',
  },
  {
    id: '4',
    number: 4,
    capacity: 2,
    status: 'available',
    location: 'outdoor',
  },
  {
    id: '5',
    number: 5,
    capacity: 4,
    status: 'available',
    location: 'outdoor',
  },
  {
    id: '6',
    number: 6,
    capacity: 8,
    status: 'available',
    location: 'private',
  },
  {
    id: '7',
    number: 7,
    capacity: 2,
    status: 'available',
    location: 'bar',
  },
  {
    id: '8',
    number: 8,
    capacity: 2,
    status: 'available',
    location: 'bar',
  },
  {
    id: '9',
    number: 9,
    capacity: 4,
    status: 'available',
    location: 'outdoor',
  },
  {
    id: '10',
    number: 10,
    capacity: 2,
    status: 'available',
    location: 'outdoor',
  },
];

interface QRCodeData {
  tableId: string;
  tableNumber: number;
  qrCodeUrl: string;
}

interface TableQRCodesPageProps {
  params: {
    slugShop: string;
    slugBranch: string;
  };


export default function TableQRCodesPage({ params }: TableQRCodesPageProps) {
  const { slugShop, slugBranch } = params;
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);

  // In a real app, we would use the merchantId from context or auth
  const merchantId = "current-merchant-id";

  // Use mock data for now, but in a real app we would use the API
  // const { data: tables, isLoading } = useGetTablesQuery(merchantId);
  const [tables, setTables] = useState(mockTables);

  // State for selected area and generated QR codes
  const [selectedArea, setSelectedArea] = useState<string>('all');
  const [qrCodes, setQrCodes] = useState<QRCodeData[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const { branch, shop } = branchWithShop;

  // Get unique areas from tables
  const areas = ['all', ...Array.from(new Set(tables.map(table => table.location)))];

  // Filter tables by selected area
  const filteredTables = selectedArea === 'all'
    ? tables
    : tables.filter(table => table.location === selectedArea);

  // Generate QR codes for the filtered tables
  const generateQRCodes = async () => {
    setIsGenerating(true);

    try {
      const baseUrl = window.location.origin;
      const qrPromises = filteredTables.map(async (table) => {
        // In a real app, this would be a URL to your ordering system with the table ID
        const orderUrl = `${baseUrl}/order?shopId=${slugShop}&branchId=${slugBranch}&tableId=${table.id}&tableNumber=${table.number}`;

        // Generate QR code as data URL
        const qrCodeUrl = await QRCode.toDataURL(orderUrl, {
          width: 300,
          margin: 2,
          color: {
            dark: '#181510', // QR code color
            light: '#fbfaf9' // Background color
          }
        });

        return {
          tableId: table.id,
          tableNumber: table.number,
          qrCodeUrl
        };
      });

      const generatedQRCodes = await Promise.all(qrPromises);
      setQrCodes(generatedQRCodes);
    } catch (error) {
      console.error('Error generating QR codes:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Download a single QR code
  const downloadQRCode = (qrCode: QRCodeData) => {
    const link = document.createElement('a');
    link.href = qrCode.qrCodeUrl;
    link.download = `table-${qrCode.tableNumber}-qr-code.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Print all QR codes
  const printQRCodes = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Table QR Codes - ${shop.name} - ${branch.name}</title>
          <style>
            body {
              font-family: 'Be Vietnam Pro', sans-serif;
              background-color: #fbfaf9;
              color: #181510;
              padding: 20px;
            }
            .qr-container {
              display: flex;
              flex-wrap: wrap;
              gap: 20px;
              justify-content: center;
            }
            .qr-item {
              text-align: center;
              margin-bottom: 30px;
              page-break-inside: avoid;
              width: 300px;
            }
            .qr-code {
              width: 100%;
              max-width: 300px;
              height: auto;
            }
            .table-number {
              font-size: 18px;
              font-weight: bold;
              margin-top: 10px;
            }
            @media print {
              @page {
                size: A4;
                margin: 1cm;
              }
            }
          </style>
        </head>
        <body>
          <div class="qr-container">
            ${qrCodes.map(qr => `
              <div class="qr-item">
                <img class="qr-code" src="${qr.qrCodeUrl}" alt="Table ${qr.tableNumber} QR Code" />
                <div class="table-number">Table ${qr.tableNumber}</div>
              </div>
            `).join('')}
          </div>
          <script>
            window.onload = function() {
              window.print();
            }
          </script>
        </body>
      </html>
    `;

    printWindow.document.open();
    printWindow.document.write(html);
    printWindow.document.close();
  };

  return (
    <div className="p-6 font-be-vietnam">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Table QR Codes</h1>
          <p className="text-[#8a745c] text-sm">Generate QR codes for each table to allow customers to scan and order directly from their tables.</p>
        </div>
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/tables`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Tables
          </Button>
        </Link>
      </div>

      {/* Table Selection */}
      <h3 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] pb-2 pt-4">Table Selection</h3>
      <div className="flex max-w-[480px] flex-wrap items-end gap-4 py-3">
        <label className="flex flex-col min-w-40 flex-1">
          <Select value={selectedArea} onValueChange={setSelectedArea}>
            <SelectTrigger className="w-full bg-[#fbfaf9] border-[#e2dcd4] h-14">
              <SelectValue placeholder="Select an area" />
            </SelectTrigger>
            <SelectContent>
              {areas.map(area => (
                <SelectItem key={area} value={area}>
                  {area === 'all' ? 'All Areas' : area.charAt(0).toUpperCase() + area.slice(1) + ' Area'}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </label>
      </div>

      {/* Generate Button */}
      <div className="flex py-3 justify-start">
        <Button
          className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]"
          onClick={generateQRCodes}
          disabled={isGenerating}
        >
          {isGenerating ? 'Generating...' : 'Generate QR Codes'}
        </Button>
      </div>

      {/* QR Code Preview */}
      {qrCodes.length > 0 && (
        <>
          <div className="flex justify-between items-center">
            <h3 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] pb-2 pt-4">QR Code Preview</h3>
            <Button
              variant="outline"
              className="border-[#e2dcd4] text-[#181510]"
              onClick={printQRCodes}
            >
              <Printer className="h-4 w-4 mr-2" />
              Print All
            </Button>
          </div>

          {qrCodes.map((qrCode) => (
            <div key={qrCode.tableId} className="mb-6">
              <div className="flex w-full grow bg-white p-4">
                <div className="w-full gap-1 overflow-hidden bg-white aspect-[3/2] rounded-xl flex">
                  <div className="w-full flex-1 flex justify-center items-center">
                    <img
                      src={qrCode.qrCodeUrl}
                      alt={`Table ${qrCode.tableNumber} QR Code`}
                      className="max-w-full max-h-full"
                    />
                  </div>
                </div>
              </div>
              <div className="flex justify-between items-center pt-1 px-4">
                <p className="text-[#8a745c] text-sm font-normal leading-normal">Table {qrCode.tableNumber}</p>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-[#8a745c] hover:text-[#181510] hover:bg-[#f1edea]"
                  onClick={() => downloadQRCode(qrCode)}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </div>
          ))}
        </>
      )}
    </div>
  );
}